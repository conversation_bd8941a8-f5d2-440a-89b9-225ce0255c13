#!/usr/bin/env python3
"""
启动CAN DBC多项目MCP服务器的脚本
"""

import sys
import os

def main():
    """启动MCP服务器"""
    print("=== CAN DBC Multi-Project MCP Server ===")
    print("正在启动多项目服务器...")

    try:
        # 导入并启动服务器
        from can_dbc_mcp_server import initialize_project_manager, mcp

        # 检查DBC文件是否存在
        dbc_files = ["F2Project.dbc", "F3Project.dbc"]
        missing_files = []

        for dbc_file in dbc_files:
            if not os.path.exists(dbc_file):
                missing_files.append(dbc_file)

        if missing_files:
            print(f"警告: 以下DBC文件不存在: {', '.join(missing_files)}")
            print("服务器将只加载可用的DBC文件")

        # 初始化多项目管理器
        initialize_project_manager()
        print("✓ 多项目管理器初始化成功")

        # 启动MCP服务器
        print("✓ MCP服务器启动中...")
        print("服务器已启动，等待客户端连接...")
        print("可用功能:")
        print("  - 多项目DBC文件管理")
        print("  - 中文关键词搜索")
        print("  - 信号值描述解析")
        print("  - 项目切换功能")
        print("按 Ctrl+C 停止服务器")

        mcp.run()

    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动服务器时发生错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
