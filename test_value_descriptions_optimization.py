#!/usr/bin/env python3
"""
测试值描述优化的整体效果
包括解析限制和紧凑格式的组合效果
"""

from can_dbc_mcp_server import (
    initialize_project_manager,
    create_compact_signal_info,
    format_signal_info
)
import can_dbc_mcp_server
import json

def test_value_descriptions_optimization():
    """测试值描述优化的整体效果"""
    print("=== 测试值描述优化整体效果 ===\n")
    
    # 初始化项目管理器
    initialize_project_manager()
    
    if not can_dbc_mcp_server.project_manager:
        print("❌ 项目管理器初始化失败")
        return
    
    parser = can_dbc_mcp_server.project_manager.get_parser("F2")
    if not parser:
        print("❌ 无法获取F2项目解析器")
        return
    
    # 找到有较多值描述的信号进行测试
    test_signals = []
    for signal_name, signal_info in parser.signals_dict.items():
        if signal_info.value_descriptions and len(signal_info.value_descriptions) >= 10:
            test_signals.append(signal_info)
            if len(test_signals) >= 5:  # 只测试前5个
                break
    
    print(f"📊 找到 {len(test_signals)} 个有较多值描述的测试信号\n")
    
    total_original_size = 0
    total_compact_size = 0
    
    for i, signal_info in enumerate(test_signals, 1):
        print(f"{i}. 信号: {signal_info.name}")
        print(f"   值描述数量: {len(signal_info.value_descriptions)}")
        
        # 测试完整格式
        full_format = format_signal_info(signal_info)
        full_json = json.dumps(full_format, ensure_ascii=False)
        full_size = len(full_json)
        
        # 测试紧凑格式
        compact_format = create_compact_signal_info(signal_info, include_details=False)
        compact_json = json.dumps(compact_format, ensure_ascii=False)
        compact_size = len(compact_json)
        
        # 计算压缩比例
        compression_ratio = compact_size / full_size
        
        print(f"   完整格式大小: {full_size} 字符")
        print(f"   紧凑格式大小: {compact_size} 字符")
        print(f"   压缩比例: {compression_ratio:.2%}")
        print(f"   节省: {full_size - compact_size} 字符")
        
        # 检查值描述处理
        full_value_count = len(full_format.get("value_descriptions", {}))
        compact_value_count = len(compact_format.get("value_descriptions", {}))
        
        print(f"   完整格式值描述: {full_value_count} 个")
        print(f"   紧凑格式值描述: {compact_value_count} 个")
        
        if "value_descriptions_note" in compact_format:
            print(f"   紧凑格式说明: {compact_format['value_descriptions_note']}")
        
        print()
        
        total_original_size += full_size
        total_compact_size += compact_size
    
    # 总体统计
    if test_signals:
        overall_compression = total_compact_size / total_original_size
        print(f"📈 总体优化效果:")
        print(f"   原始总大小: {total_original_size} 字符")
        print(f"   紧凑总大小: {total_compact_size} 字符")
        print(f"   总体压缩比例: {overall_compression:.2%}")
        print(f"   总体节省: {total_original_size - total_compact_size} 字符")
        print(f"   平均每信号节省: {(total_original_size - total_compact_size) / len(test_signals):.0f} 字符")

def test_value_descriptions_in_search():
    """测试搜索结果中的值描述优化"""
    print(f"\n=== 测试搜索结果中的值描述优化 ===\n")
    
    if not can_dbc_mcp_server.project_manager:
        initialize_project_manager()
    
    parser = can_dbc_mcp_server.project_manager.get_parser("F2")
    if not parser:
        print("❌ 无法获取F2项目解析器")
        return
    
    # 执行一个会返回多个有值描述信号的搜索
    keyword = "ADS"
    signals = parser.search_signals_fuzzy(keyword, case_sensitive=False)
    
    # 统计有值描述的信号
    signals_with_values = [s for s in signals if s.value_descriptions]
    
    print(f"🔍 搜索关键词: '{keyword}'")
    print(f"   总匹配信号: {len(signals)}")
    print(f"   有值描述的信号: {len(signals_with_values)}")
    
    if signals_with_values:
        # 分析前10个有值描述的信号
        sample_signals = signals_with_values[:10]
        
        total_value_descriptions = 0
        max_values = 0
        min_values = float('inf')
        
        for signal in sample_signals:
            count = len(signal.value_descriptions)
            total_value_descriptions += count
            max_values = max(max_values, count)
            min_values = min(min_values, count)
        
        avg_values = total_value_descriptions / len(sample_signals)
        
        print(f"\n📊 前10个有值描述信号的统计:")
        print(f"   平均值描述数量: {avg_values:.1f}")
        print(f"   最大值描述数量: {max_values}")
        print(f"   最小值描述数量: {min_values}")
        print(f"   总值描述数量: {total_value_descriptions}")
        
        # 验证20个限制是否生效
        over_limit = [s for s in sample_signals if len(s.value_descriptions) > 20]
        if over_limit:
            print(f"   ❌ 发现 {len(over_limit)} 个信号超过20个值描述限制")
        else:
            print(f"   ✅ 所有信号都在20个值描述限制内")
        
        # 显示几个示例
        print(f"\n📋 示例信号值描述:")
        for i, signal in enumerate(sample_signals[:3], 1):
            print(f"   {i}. {signal.name}: {len(signal.value_descriptions)} 个值描述")
            # 显示前3个值描述
            for j, (value, desc) in enumerate(list(signal.value_descriptions.items())[:3]):
                print(f"      {value}: {desc}")
            if len(signal.value_descriptions) > 3:
                print(f"      ... 还有 {len(signal.value_descriptions) - 3} 个")

def test_memory_usage_estimation():
    """估算内存使用优化效果"""
    print(f"\n=== 估算内存使用优化效果 ===\n")
    
    if not can_dbc_mcp_server.project_manager:
        initialize_project_manager()
    
    # 统计两个项目的值描述情况
    projects = ["F2", "F3"]
    total_optimization = {"before": 0, "after": 0}
    
    for project_key in projects:
        parser = can_dbc_mcp_server.project_manager.get_parser(project_key)
        if parser:
            config = can_dbc_mcp_server.project_manager.project_configs[project_key]
            print(f"📊 {config['name']} 内存优化估算:")
            
            signals_with_values = 0
            current_total_values = 0
            estimated_without_limit = 0
            
            for signal_info in parser.signals_dict.values():
                if signal_info.value_descriptions:
                    signals_with_values += 1
                    current_count = len(signal_info.value_descriptions)
                    current_total_values += current_count
                    
                    # 估算如果没有限制可能的数量（假设平均会有更多）
                    # 这里我们假设某些信号可能原本有更多值描述
                    if current_count == 20:  # 达到限制的信号
                        estimated_without_limit += 30  # 假设原本可能有30个
                    else:
                        estimated_without_limit += current_count
            
            memory_saved = estimated_without_limit - current_total_values
            
            print(f"   有值描述的信号数: {signals_with_values}")
            print(f"   当前值描述总数: {current_total_values}")
            print(f"   估算无限制时: {estimated_without_limit}")
            print(f"   节省的值描述数: {memory_saved}")
            print(f"   内存优化比例: {memory_saved / estimated_without_limit:.1%}")
            print()
            
            total_optimization["before"] += estimated_without_limit
            total_optimization["after"] += current_total_values
    
    # 总体优化效果
    total_saved = total_optimization["before"] - total_optimization["after"]
    total_ratio = total_saved / total_optimization["before"]
    
    print(f"🎯 总体内存优化效果:")
    print(f"   估算无限制总值描述: {total_optimization['before']}")
    print(f"   当前限制后总值描述: {total_optimization['after']}")
    print(f"   总共节省值描述数: {total_saved}")
    print(f"   总体优化比例: {total_ratio:.1%}")

if __name__ == "__main__":
    test_value_descriptions_optimization()
    test_value_descriptions_in_search()
    test_memory_usage_estimation()
    
    print("\n" + "="*50)
    print("🎉 值描述优化测试完成！")
    print("\n💡 优化总结:")
    print("   ✅ 解析时限制每个信号最多20个值描述")
    print("   ✅ 紧凑格式进一步限制为5个值描述")
    print("   ✅ 显著减少内存使用和token消耗")
    print("   ✅ 保持核心信息的完整性")
