#!/usr/bin/env python3
"""
测试改进的模糊查询功能
"""

from dbc_parser import MultiProjectDBCManager

def test_multi_keyword_search():
    """测试多关键词搜索功能"""
    print("=== 测试改进的模糊查询功能 ===\n")
    
    # 初始化项目管理器
    manager = MultiProjectDBCManager()
    manager.load_all_projects()
    manager.set_current_project("F2")
    
    parser = manager.get_current_parser()
    if not parser:
        print("❌ 无法获取F2项目解析器")
        return
    
    # 测试用例
    test_cases = [
        {
            "name": "多关键词搜索（中英文混合）",
            "keyword": "超声波 雷达 距离 ultrasonic radar distance",
            "description": "模拟大模型传入的多关键词查询"
        },
        {
            "name": "单一中文关键词",
            "keyword": "制动",
            "description": "测试中文关键词搜索"
        },
        {
            "name": "英文关键词组合",
            "keyword": "ADS brake torque",
            "description": "测试英文关键词组合"
        },
        {
            "name": "传感器相关搜索",
            "keyword": "sensor ultrasonic 传感器",
            "description": "测试传感器相关信号搜索"
        },
        {
            "name": "时间相关搜索",
            "keyword": "time 时间 year month",
            "description": "测试时间相关信号搜索"
        },
        {
            "name": "MDC相关搜索",
            "keyword": "MDC ultrasonic radar",
            "description": "测试MDC发送的超声波雷达信号"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. {test_case['name']}")
        print(f"   关键词: '{test_case['keyword']}'")
        print(f"   说明: {test_case['description']}")
        
        # 执行搜索
        results = parser.search_signals_fuzzy(test_case['keyword'], case_sensitive=False)
        
        print(f"   结果: 找到 {len(results)} 个匹配信号")
        
        # 显示前5个结果
        for j, signal in enumerate(results[:5]):
            print(f"      {j+1}. {signal.name}")
            print(f"         消息: {signal.message_name}")
            if signal.comment:
                print(f"         注释: {signal.comment}")
            if signal.value_descriptions:
                desc_preview = list(signal.value_descriptions.items())[:2]
                print(f"         值描述: {desc_preview}...")
        
        if len(results) > 5:
            print(f"      ... 还有 {len(results) - 5} 个信号")
        
        print()

def test_keyword_splitting():
    """测试关键词分割功能"""
    print("=== 测试关键词分割功能 ===\n")
    
    manager = MultiProjectDBCManager()
    manager.load_project("F2")
    parser = manager.get_parser("F2")
    
    test_keywords = [
        "超声波 雷达 距离 ultrasonic radar distance",
        "ADS,brake,torque",
        "time；时间；year，month",
        "sensor  ultrasonic   传感器",
        "single_keyword"
    ]
    
    for keyword in test_keywords:
        split_result = parser._split_keywords(keyword)
        print(f"原始: '{keyword}'")
        print(f"分割: {split_result}")
        print()

def test_scoring_system():
    """测试评分系统"""
    print("=== 测试评分系统 ===\n")
    
    manager = MultiProjectDBCManager()
    manager.load_project("F2")
    parser = manager.get_parser("F2")
    
    # 获取一些测试信号
    test_signals = ["ADS_BrkTarTqEna", "TBOX_TimeYear", "VDC_brakePedalSts"]
    
    for signal_name in test_signals:
        signal_info = parser.get_signal_by_name(signal_name)
        if signal_info:
            print(f"信号: {signal_name}")
            print(f"消息: {signal_info.message_name}")
            print(f"注释: {signal_info.comment}")
            
            # 测试不同关键词的匹配分数
            test_keywords_list = [
                ["ADS"],
                ["brake", "制动"],
                ["torque", "扭矩"],
                ["ADS", "brake", "torque"],
                ["time", "时间"],
                ["year", "年"]
            ]
            
            for keywords in test_keywords_list:
                score = parser._calculate_match_score(signal_info, keywords, case_sensitive=False)
                print(f"   关键词 {keywords}: 分数 {score:.3f}")
            
            print()

if __name__ == "__main__":
    test_keyword_splitting()
    test_scoring_system()
    test_multi_keyword_search()
