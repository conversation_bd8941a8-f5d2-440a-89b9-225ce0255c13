# 多项目CAN DBC MCP服务器使用示例

本文档提供了多项目CAN DBC MCP服务器的详细使用示例。

## 1. 项目管理

### 获取可用项目列表
```json
// 调用: get_available_projects()
{
  "available_projects": [
    {
      "key": "F2",
      "name": "F2项目",
      "file": "F2Project.dbc",
      "description": "F2项目CAN信号数据库",
      "status": "已加载 (当前)",
      "loaded": true,
      "current": true
    },
    {
      "key": "F3",
      "name": "F3项目",
      "file": "F3Project.dbc",
      "description": "F3项目CAN信号数据库",
      "status": "已加载",
      "loaded": true,
      "current": false
    }
  ],
  "current_project": "F2",
  "total_projects": 2,
  "loaded_projects": 2
}
```

### 切换项目
```json
// 调用: switch_project("F3")
{
  "success": true,
  "current_project": "F3",
  "project_name": "F3项目",
  "project_description": "F3项目CAN信号数据库",
  "statistics": {
    "total_messages": 73,
    "total_signals": 1268,
    "dbc_file": "F3Project.dbc",
    "database_name": "Unknown"
  }
}
```

## 2. 信号查询

### 精确信号查询（F2项目）
```json
// 调用: get_signal_exact("ADS_BrkTarTqEna", "F2")
{
  "found": true,
  "signal": {
    "name": "ADS_BrkTarTqEna",
    "message_name": "ADS_brake_11B",
    "message_id": "0x11B",
    "message_id_decimal": 283,
    "start_bit": 7,
    "length": 1,
    "factor": 1.0,
    "offset": 0.0,
    "minimum": 0.0,
    "maximum": 1.0,
    "unit": "",
    "receivers": ["IPB_CHS", "RBU_CHS"],
    "byte_order": "big_endian",
    "is_signed": false,
    "comment": "ADS制动力矩使能",
    "value_descriptions": {
      "0": "Disable",
      "1": "Enable"
    },
    "project_key": "F2",
    "project_name": "F2项目"
  }
}
```

### 中文关键词模糊搜索
```json
// 调用: search_signals_fuzzy("ADS制动", false, 10, "F2")
{
  "keyword": "ADS制动",
  "case_sensitive": false,
  "total_found": 2,
  "returned_count": 2,
  "max_results": 10,
  "project_key": "F2",
  "project_name": "F2项目",
  "signals": [
    {
      "name": "IPB_DrvrBrkOvrd",
      "message_name": "IPB_brake_11A",
      "comment": "驾驶员制动踏板覆盖",
      "value_descriptions": {
        "0": "No Override",
        "1": "Override"
      },
      "project_key": "F2"
    },
    {
      "name": "RBU_DrvrBrkOvrd",
      "message_name": "RBU_brake_11C",
      "comment": "驾驶员制动踏板覆盖",
      "value_descriptions": {},
      "project_key": "F2"
    }
  ]
}
```

## 3. 消息查询

### 获取消息信息
```json
// 调用: get_message_info("TBOX_TimeInfo", "F3")
{
  "found": true,
  "message": {
    "name": "TBOX_TimeInfo",
    "message_id": "0x5E2",
    "message_id_decimal": 1506,
    "length": 8,
    "sender": "TBOX",
    "signals": ["TBOX_TimeYear", "TBOX_TimeMonth", "TBOX_TimeDay", "TBOX_TimeHour", "TBOX_TimeMinute"],
    "signal_count": 5,
    "comment": "时间信息",
    "project_key": "F3",
    "project_name": "F3项目"
  }
}
```

### 获取消息下的所有信号
```json
// 调用: get_signals_by_message("ADS_brake_11B", "F2")
{
  "message_name": "ADS_brake_11B",
  "signal_count": 3,
  "project_key": "F2",
  "project_name": "F2项目",
  "signals": [
    {
      "name": "ADS_BrkTarTqEna",
      "start_bit": 7,
      "length": 1,
      "comment": "ADS制动力矩使能",
      "value_descriptions": {
        "0": "Disable",
        "1": "Enable"
      },
      "project_key": "F2"
    },
    {
      "name": "ADS_BrkTarTq",
      "start_bit": 15,
      "length": 16,
      "comment": "ADS制动目标力矩",
      "value_descriptions": {},
      "project_key": "F2"
    }
  ]
}
```

## 4. 统计信息

### 获取所有项目统计信息
```json
// 调用: get_project_statistics()
{
  "total_projects": 2,
  "loaded_projects": 2,
  "current_project": "F2",
  "projects": {
    "F2": {
      "total_messages": 79,
      "total_signals": 1566,
      "dbc_file": "F2Project.dbc",
      "database_name": "Unknown",
      "project_name": "F2项目",
      "project_description": "F2项目CAN信号数据库"
    },
    "F3": {
      "total_messages": 73,
      "total_signals": 1268,
      "dbc_file": "F3Project.dbc",
      "database_name": "Unknown",
      "project_name": "F3项目",
      "project_description": "F3项目CAN信号数据库"
    }
  }
}
```

### 获取单个项目统计信息
```json
// 调用: get_project_statistics("F3")
{
  "total_messages": 73,
  "total_signals": 1268,
  "dbc_file": "F3Project.dbc",
  "database_name": "Unknown",
  "project_key": "F3",
  "project_name": "F3项目",
  "project_description": "F3项目CAN信号数据库"
}
```

## 5. 常见使用场景

### 场景1：查找特定功能的信号
1. 使用中文关键词模糊搜索：`search_signals_fuzzy("制动")`
2. 查看搜索结果中的信号详情
3. 根据需要切换到不同项目进行对比

### 场景2：分析消息结构
1. 获取消息信息：`get_message_info("ADS_brake_11B")`
2. 获取消息下所有信号：`get_signals_by_message("ADS_brake_11B")`
3. 查看每个信号的值描述和注释

### 场景3：项目对比分析
1. 获取所有项目统计：`get_project_statistics()`
2. 在不同项目中搜索相同关键词
3. 对比不同项目的信号定义差异

## 6. 错误处理

### 项目不存在
```json
{
  "error": "项目 F4 不存在或加载失败",
  "available_projects": ["F2", "F3"]
}
```

### 信号未找到
```json
{
  "found": false,
  "error": "在项目 F2 中未找到信号: NonExistentSignal",
  "project_key": "F2"
}
```

### 当前无活动项目
```json
{
  "error": "当前没有活动项目，请先切换项目"
}
```
