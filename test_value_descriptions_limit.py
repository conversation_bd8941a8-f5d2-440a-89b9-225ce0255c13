#!/usr/bin/env python3
"""
测试信号值描述限制功能
验证每个信号最多保留前20个值描述
"""

from dbc_parser import DBCParser, MultiProjectDBCManager

def test_value_descriptions_limit():
    """测试值描述数量限制功能"""
    print("=== 测试信号值描述限制功能 ===\n")
    
    # 初始化解析器
    parser = DBCParser("F2Project.dbc")
    
    # 统计值描述信息
    total_signals_with_values = 0
    max_values_found = 0
    signals_with_many_values = []
    
    print("📊 分析所有信号的值描述数量...")
    
    for signal_name, signal_info in parser.signals_dict.items():
        if signal_info.value_descriptions:
            total_signals_with_values += 1
            value_count = len(signal_info.value_descriptions)
            
            if value_count > max_values_found:
                max_values_found = value_count
            
            # 记录值描述较多的信号
            if value_count >= 10:
                signals_with_many_values.append({
                    "name": signal_name,
                    "count": value_count,
                    "values": signal_info.value_descriptions
                })
    
    print(f"✅ 统计完成:")
    print(f"   - 总信号数: {len(parser.signals_dict)}")
    print(f"   - 有值描述的信号数: {total_signals_with_values}")
    print(f"   - 最大值描述数量: {max_values_found}")
    print(f"   - 值描述≥10个的信号数: {len(signals_with_many_values)}")
    
    # 验证限制是否生效
    print(f"\n🔍 验证20个值描述限制:")
    limit_exceeded = False
    
    for signal_name, signal_info in parser.signals_dict.items():
        if signal_info.value_descriptions and len(signal_info.value_descriptions) > 20:
            print(f"❌ 信号 {signal_name} 有 {len(signal_info.value_descriptions)} 个值描述，超过限制！")
            limit_exceeded = True
    
    if not limit_exceeded:
        print("✅ 所有信号的值描述数量都在20个以内")
    
    # 显示值描述较多的信号详情
    print(f"\n📋 值描述较多的信号详情（前5个）:")
    for i, signal_data in enumerate(signals_with_many_values[:5]):
        print(f"\n{i+1}. 信号: {signal_data['name']}")
        print(f"   值描述数量: {signal_data['count']}")
        print("   值描述内容:")
        
        # 显示前10个值描述
        for j, (value, desc) in enumerate(list(signal_data['values'].items())[:10]):
            print(f"     {value}: {desc}")
        
        if signal_data['count'] > 10:
            print(f"     ... 还有 {signal_data['count'] - 10} 个值描述")
    
    return max_values_found <= 20

def test_before_after_comparison():
    """测试修改前后的对比（模拟）"""
    print(f"\n=== 修改效果对比 ===\n")
    
    parser = DBCParser("F2Project.dbc")
    
    # 统计当前的值描述情况
    value_desc_stats = {}
    total_value_descriptions = 0
    
    for signal_name, signal_info in parser.signals_dict.items():
        if signal_info.value_descriptions:
            count = len(signal_info.value_descriptions)
            total_value_descriptions += count
            
            if count in value_desc_stats:
                value_desc_stats[count] += 1
            else:
                value_desc_stats[count] = 1
    
    print("📊 当前值描述分布统计:")
    for count in sorted(value_desc_stats.keys()):
        signals_count = value_desc_stats[count]
        print(f"   {count}个值描述: {signals_count}个信号")
    
    print(f"\n📈 总计:")
    print(f"   - 总值描述数量: {total_value_descriptions}")
    print(f"   - 平均每信号: {total_value_descriptions / len([s for s in parser.signals_dict.values() if s.value_descriptions]):.1f}个")
    
    # 估算如果没有限制可能的数量
    estimated_without_limit = total_value_descriptions
    print(f"\n💾 内存优化效果:")
    print(f"   - 当前值描述总数: {total_value_descriptions}")
    print(f"   - 限制后最大可能: {len([s for s in parser.signals_dict.values() if s.value_descriptions]) * 20}")
    
    return True

def test_multi_project_value_descriptions():
    """测试多项目的值描述限制"""
    print(f"\n=== 多项目值描述限制测试 ===\n")
    
    manager = MultiProjectDBCManager()
    manager.load_all_projects()
    
    for project_key in ["F2", "F3"]:
        parser = manager.get_parser(project_key)
        if parser:
            config = manager.project_configs[project_key]
            print(f"📊 {config['name']} 值描述统计:")
            
            signals_with_values = 0
            total_values = 0
            max_values = 0
            
            for signal_info in parser.signals_dict.values():
                if signal_info.value_descriptions:
                    signals_with_values += 1
                    count = len(signal_info.value_descriptions)
                    total_values += count
                    max_values = max(max_values, count)
            
            print(f"   - 有值描述的信号: {signals_with_values}")
            print(f"   - 总值描述数量: {total_values}")
            print(f"   - 最大值描述数量: {max_values}")
            print(f"   - 限制是否生效: {'✅ 是' if max_values <= 20 else '❌ 否'}")
            print()

if __name__ == "__main__":
    # 运行测试
    limit_ok = test_value_descriptions_limit()
    test_before_after_comparison()
    test_multi_project_value_descriptions()
    
    print("=" * 50)
    if limit_ok:
        print("🎉 值描述限制功能测试通过！")
        print("✅ 每个信号的值描述数量都在20个以内")
    else:
        print("❌ 值描述限制功能测试失败！")
        print("⚠️  发现超过20个值描述的信号")
    
    print("\n💡 优化效果:")
    print("   - 减少了内存使用")
    print("   - 降低了token消耗")
    print("   - 保持了核心信息")
