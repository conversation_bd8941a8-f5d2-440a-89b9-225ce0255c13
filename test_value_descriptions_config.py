#!/usr/bin/env python3
"""
测试信号值描述的排序和配置功能
验证按key值0-N排序和可配置数量限制
"""

from dbc_parser import DBCParser, MultiProjectDBCManager

def test_value_descriptions_sorting_and_config():
    """测试值描述排序和配置功能"""
    print("=== 测试信号值描述排序和配置功能 ===\n")
    
    # 测试不同的配置值
    test_configs = [5, 10, 15, 20]
    
    for max_values in test_configs:
        print(f"📊 测试配置: 最大值描述数量 = {max_values}")
        
        # 使用指定配置创建解析器
        parser = DBCParser("F2Project.dbc", max_value_descriptions=max_values)
        
        # 统计值描述情况
        signals_with_values = 0
        total_values = 0
        max_key_found = 0
        min_key_found = float('inf')
        signals_over_limit = 0
        
        # 找一些有值描述的信号进行详细分析
        sample_signals = []
        
        for signal_name, signal_info in parser.signals_dict.items():
            if signal_info.value_descriptions:
                signals_with_values += 1
                count = len(signal_info.value_descriptions)
                total_values += count
                
                # 检查key值范围
                keys = list(signal_info.value_descriptions.keys())
                if keys:
                    max_key = max(keys)
                    min_key = min(keys)
                    max_key_found = max(max_key_found, max_key)
                    min_key_found = min(min_key_found, min_key)
                
                # 检查是否有超过配置限制的key值
                over_limit_keys = [k for k in keys if k > max_values]
                if over_limit_keys:
                    signals_over_limit += 1
                
                # 收集样本信号
                if len(sample_signals) < 3 and count >= 5:
                    sample_signals.append({
                        "name": signal_name,
                        "values": signal_info.value_descriptions,
                        "count": count
                    })
        
        print(f"   有值描述的信号数: {signals_with_values}")
        print(f"   总值描述数量: {total_values}")
        print(f"   最大key值: {max_key_found}")
        print(f"   最小key值: {min_key_found}")
        print(f"   有超限key值的信号数: {signals_over_limit}")
        
        # 验证排序和范围限制
        all_keys_in_range = True
        all_keys_sorted = True
        
        for signal_name, signal_info in parser.signals_dict.items():
            if signal_info.value_descriptions:
                keys = list(signal_info.value_descriptions.keys())
                
                # 检查key值范围
                for key in keys:
                    if not (0 <= key <= max_values):
                        all_keys_in_range = False
                        break
                
                # 检查排序
                if keys != sorted(keys):
                    all_keys_sorted = False
                    break
        
        print(f"   ✅ 所有key值在0-{max_values}范围内: {all_keys_in_range}")
        print(f"   ✅ 所有key值已排序: {all_keys_sorted}")
        
        # 显示样本信号
        if sample_signals:
            print(f"   📋 样本信号:")
            for i, signal in enumerate(sample_signals, 1):
                print(f"      {i}. {signal['name']} ({signal['count']}个值描述)")
                sorted_items = sorted(signal['values'].items())
                for j, (key, desc) in enumerate(sorted_items[:5]):
                    print(f"         {key}: {desc}")
                if len(sorted_items) > 5:
                    print(f"         ... 还有 {len(sorted_items) - 5} 个")
        
        print()

def test_multi_project_with_config():
    """测试多项目管理器的配置功能"""
    print("=== 测试多项目管理器配置功能 ===\n")
    
    # 测试不同配置的多项目管理器
    test_configs = [10, 20]
    
    for max_values in test_configs:
        print(f"📊 多项目管理器配置: 最大值描述数量 = {max_values}")
        
        manager = MultiProjectDBCManager(max_value_descriptions=max_values)
        manager.load_all_projects()
        
        for project_key in ["F2", "F3"]:
            parser = manager.get_parser(project_key)
            if parser:
                config = manager.project_configs[project_key]
                print(f"   {config['name']}:")
                
                # 验证配置是否正确传递
                print(f"     配置的最大值描述数: {parser.max_value_descriptions}")
                
                # 统计实际情况
                signals_with_values = 0
                total_values = 0
                max_key = 0
                
                for signal_info in parser.signals_dict.values():
                    if signal_info.value_descriptions:
                        signals_with_values += 1
                        total_values += len(signal_info.value_descriptions)
                        if signal_info.value_descriptions:
                            max_key = max(max_key, max(signal_info.value_descriptions.keys()))
                
                print(f"     有值描述的信号数: {signals_with_values}")
                print(f"     总值描述数量: {total_values}")
                print(f"     实际最大key值: {max_key}")
                print(f"     配置限制生效: {'✅' if max_key <= max_values else '❌'}")
        
        print()

def test_key_range_validation():
    """测试key值范围验证"""
    print("=== 测试key值范围验证 ===\n")
    
    # 使用较小的限制来测试
    parser = DBCParser("F2Project.dbc", max_value_descriptions=10)
    
    print("📊 验证key值范围限制 (限制: 0-10)")
    
    violations = []
    total_signals_checked = 0
    
    for signal_name, signal_info in parser.signals_dict.items():
        if signal_info.value_descriptions:
            total_signals_checked += 1
            for key in signal_info.value_descriptions.keys():
                if not (0 <= key <= 10):
                    violations.append({
                        "signal": signal_name,
                        "key": key,
                        "description": signal_info.value_descriptions[key]
                    })
    
    print(f"   检查的信号数: {total_signals_checked}")
    print(f"   违规的key值数: {len(violations)}")
    
    if violations:
        print("   ❌ 发现违规key值:")
        for violation in violations[:5]:  # 只显示前5个
            print(f"      信号: {violation['signal']}, key: {violation['key']}")
        if len(violations) > 5:
            print(f"      ... 还有 {len(violations) - 5} 个违规")
    else:
        print("   ✅ 所有key值都在范围内")
    
    # 测试排序
    print(f"\n📊 验证key值排序")
    unsorted_signals = []
    
    for signal_name, signal_info in parser.signals_dict.items():
        if signal_info.value_descriptions:
            keys = list(signal_info.value_descriptions.keys())
            if keys != sorted(keys):
                unsorted_signals.append({
                    "signal": signal_name,
                    "keys": keys,
                    "sorted_keys": sorted(keys)
                })
    
    print(f"   未排序的信号数: {len(unsorted_signals)}")
    
    if unsorted_signals:
        print("   ❌ 发现未排序的信号:")
        for signal in unsorted_signals[:3]:
            print(f"      信号: {signal['signal']}")
            print(f"      原始: {signal['keys']}")
            print(f"      排序: {signal['sorted_keys']}")
    else:
        print("   ✅ 所有信号的key值都已正确排序")

def test_configuration_comparison():
    """对比不同配置的效果"""
    print(f"\n=== 对比不同配置的效果 ===\n")
    
    configs = [5, 10, 15, 20]
    results = {}
    
    for config in configs:
        parser = DBCParser("F2Project.dbc", max_value_descriptions=config)
        
        signals_with_values = 0
        total_values = 0
        
        for signal_info in parser.signals_dict.values():
            if signal_info.value_descriptions:
                signals_with_values += 1
                total_values += len(signal_info.value_descriptions)
        
        results[config] = {
            "signals_with_values": signals_with_values,
            "total_values": total_values,
            "avg_values_per_signal": total_values / signals_with_values if signals_with_values > 0 else 0
        }
    
    print("📊 不同配置对比:")
    print("配置 | 有值描述信号数 | 总值描述数 | 平均每信号")
    print("-" * 50)
    
    for config in configs:
        data = results[config]
        print(f"{config:4d} | {data['signals_with_values']:12d} | {data['total_values']:9d} | {data['avg_values_per_signal']:8.1f}")
    
    # 计算节省效果
    baseline = results[20]
    print(f"\n💾 相对于配置20的节省效果:")
    for config in [5, 10, 15]:
        current = results[config]
        saved_values = baseline['total_values'] - current['total_values']
        save_ratio = saved_values / baseline['total_values'] * 100
        print(f"   配置{config}: 节省 {saved_values} 个值描述 ({save_ratio:.1f}%)")

if __name__ == "__main__":
    test_value_descriptions_sorting_and_config()
    test_multi_project_with_config()
    test_key_range_validation()
    test_configuration_comparison()
    
    print("=" * 50)
    print("🎉 信号值描述排序和配置功能测试完成！")
    print("\n💡 功能总结:")
    print("   ✅ 支持可配置的值描述数量限制")
    print("   ✅ 按key值0-N范围过滤")
    print("   ✅ key值自动排序")
    print("   ✅ 多项目管理器支持配置传递")
