# CAN DBC Multi-Project MCP Server

基于Python FAST_MCP框架构建的多项目CAN DBC文件解析MCP服务器，提供CAN信号的精确查询和模糊查询功能，支持中文关键词搜索、信号值描述解析和多项目管理。

## 功能特性

- **多项目支持**: 同时管理多个DBC项目文件（F2项目、F3项目等）
- **项目切换**: 动态切换当前活动项目
- **精确信号查询**: 根据信号名称精确查找CAN信号信息
- **模糊信号搜索**: 支持关键词模糊搜索，包括中文关键词
- **消息信息查询**: 查询CAN消息的详细信息
- **消息模糊搜索**: 支持消息名称的模糊搜索
- **信号按消息查询**: 获取指定消息下的所有信号
- **项目统计信息**: 获取单个项目或所有项目的统计信息
- **信号值描述**: 解析并显示信号的值描述（VAL_条目）
- **中文编码支持**: 自动处理中文注释和信号名称的编码转换

## 安装依赖

```bash
pip install -r requirements.txt
```

## 文件结构

```
├── F2Project.dbc           # F2项目DBC文件
├── F3Project.dbc           # F3项目DBC文件
├── dbc_parser.py          # DBC文件解析器模块（支持多项目）
├── can_dbc_mcp_server.py  # MCP服务器主程序
├── test_dbc_parser.py     # 原始测试脚本
├── test_multi_project.py  # 多项目功能测试脚本
├── cline_mcp_config.json  # Cline MCP配置文件
├── requirements.txt       # Python依赖包
└── README.md             # 说明文档
```

## 使用方法

### 1. 启动MCP服务器

```bash
python can_dbc_mcp_server.py
```

### 2. 可用的MCP工具

#### `get_available_projects()`
获取可用项目列表

**返回:** 可用项目信息和当前项目状态

#### `switch_project(project_key: str)`
切换当前活动项目

**参数:**
- `project_key`: 项目键值 (F2, F3)

**返回:** 切换结果和项目信息

#### `get_signal_exact(signal_name: str, project_key: Optional[str] = None)`
精确查询CAN信号信息

**参数:**
- `signal_name`: 信号名称（精确匹配）
- `project_key`: 项目键值 (F2, F3)，如果不指定则使用当前项目

**返回:** 信号详细信息字典

**示例:**
```json
{
  "found": true,
  "signal": {
    "name": "TBOX_TimeYear",
    "message_name": "TBOX_TimeInfo",
    "message_id": "0x5E2",
    "message_id_decimal": 1506,
    "start_bit": 7,
    "length": 8,
    "factor": 1,
    "offset": 2000,
    "minimum": 2000,
    "maximum": 2255,
    "unit": "Year",
    "receivers": ["MDC_CHS", "IPB_CHS", "RBU_CHS", "EPS_CHS", "ABM"],
    "byte_order": "big_endian",
    "is_signed": false,
    "comment": null
  }
}
```

#### `search_signals_fuzzy(keyword: str, case_sensitive: bool = False, max_results: int = 50, project_key: Optional[str] = None)`
模糊查询CAN信号信息（支持中文关键词搜索）

**参数:**
- `keyword`: 搜索关键词（支持部分匹配，可搜索信号名称、注释、值描述）
- `case_sensitive`: 是否区分大小写，默认False
- `max_results`: 最大返回结果数量，默认50
- `project_key`: 项目键值 (F2, F3)，如果不指定则使用当前项目

**返回:** 匹配的信号列表和统计信息，包含信号值描述

#### `get_message_info(message_name: str, project_key: Optional[str] = None)`
查询CAN消息信息

**参数:**
- `message_name`: 消息名称
- `project_key`: 项目键值 (F2, F3)，如果不指定则使用当前项目

**返回:** 消息详细信息字典

#### `search_messages_fuzzy(keyword: str, case_sensitive: bool = False, max_results: int = 50, project_key: Optional[str] = None)`
模糊查询CAN消息信息

**参数:**
- `keyword`: 搜索关键词
- `case_sensitive`: 是否区分大小写，默认False
- `max_results`: 最大返回结果数量，默认50
- `project_key`: 项目键值 (F2, F3)，如果不指定则使用当前项目

**返回:** 匹配的消息列表和统计信息

#### `get_signals_by_message(message_name: str, project_key: Optional[str] = None)`
根据消息名称获取该消息下的所有信号

**参数:**
- `message_name`: 消息名称
- `project_key`: 项目键值 (F2, F3)，如果不指定则使用当前项目

**返回:** 该消息下的所有信号信息

#### `get_project_statistics(project_key: Optional[str] = None)`
获取项目统计信息

**参数:**
- `project_key`: 项目键值 (F2, F3)，如果不指定则返回所有项目统计信息

**返回:** 项目统计信息

## 测试

运行测试脚本验证功能：

```bash
# 测试基础DBC解析功能
python test_dbc_parser.py

# 测试多项目功能
python test_multi_project.py
```

## 项目DBC文件信息

### F2项目 (F2Project.dbc)
- **79个CAN消息**
- **1566个CAN信号**
- 涵盖车辆各个系统的信号定义
- 支持中文注释和信号值描述

### F3项目 (F3Project.dbc)
- **73个CAN消息**
- **1268个CAN信号**
- 包含时间信息、车辆状态等信号定义

## 技术栈

- **Python 3.12+**
- **FastMCP**: MCP服务器框架
- **cantools**: DBC文件解析库
- **typing-extensions**: 类型注解支持

## 开发说明

### 数据结构

#### SignalInfo
```python
@dataclass
class SignalInfo:
    name: str                           # 信号名称
    message_name: str                   # 所属消息名称
    message_id: int                     # 消息ID
    start_bit: int                      # 起始位
    length: int                         # 长度（位）
    factor: float                       # 缩放因子
    offset: float                       # 偏移量
    minimum: Optional[float]            # 最小值
    maximum: Optional[float]            # 最大值
    unit: str                           # 单位
    receivers: List[str]                # 接收者列表
    byte_order: str                     # 字节序
    is_signed: bool                     # 是否有符号
    comment: Optional[str]              # 注释
    value_descriptions: Dict[int, str]  # 信号值描述（VAL_条目）
```

#### MultiProjectDBCManager
```python
class MultiProjectDBCManager:
    """多项目DBC文件管理器"""

    def __init__(self):
        self.projects: Dict[str, DBCParser] = {}
        self.current_project: Optional[str] = None
        self.project_configs = {
            "F2": {"name": "F2项目", "file": "F2Project.dbc"},
            "F3": {"name": "F3项目", "file": "F3Project.dbc"}
        }
```

#### MessageInfo
```python
@dataclass
class MessageInfo:
    name: str                   # 消息名称
    message_id: int             # 消息ID
    length: int                 # 长度（字节）
    sender: str                 # 发送者
    signals: List[str]          # 信号列表
    comment: Optional[str]      # 注释
```

## 许可证

MIT License
## 使用方法
启动MCP服务器
python start_server.py
查看功能演示
python demo.py
运行测试
python test_dbc_parser.py