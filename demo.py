#!/usr/bin/env python3
"""
CAN DBC多项目MCP Server功能演示脚本
"""

from dbc_parser import MultiProjectDBCManager

def print_separator(title):
    """打印分隔符"""
    print(f"\n{'='*50}")
    print(f" {title}")
    print(f"{'='*50}")

def demo_multi_project_manager():
    """演示多项目DBC管理器功能"""
    print_separator("多项目CAN DBC解析器功能演示")

    try:
        # 初始化多项目管理器
        manager = MultiProjectDBCManager()
        print("✓ 多项目管理器初始化成功")

        # 加载所有项目
        print_separator("项目加载演示")
        results = manager.load_all_projects()
        print("📁 项目加载结果:")
        for project_key, success in results.items():
            config = manager.project_configs[project_key]
            status = "✅ 成功" if success else "❌ 失败"
            print(f"   {config['name']}: {status}")

        # 显示项目统计信息
        stats = manager.get_project_statistics()
        print(f"\n📊 多项目统计信息:")
        print(f"   - 总项目数: {stats['total_projects']}")
        print(f"   - 已加载项目数: {stats['loaded_projects']}")
        print(f"   - 当前项目: {stats['current_project']}")

        for project_key, project_stats in stats['projects'].items():
            print(f"\n   📋 {project_key}项目:")
            print(f"      - 消息数: {project_stats['total_messages']}")
            print(f"      - 信号数: {project_stats['total_signals']}")
            print(f"      - 文件: {project_stats['dbc_file']}")

        # 演示项目切换
        print_separator("项目切换演示")
        for project_key in ["F2", "F3"]:
            success = manager.set_current_project(project_key)
            if success:
                config = manager.project_configs[project_key]
                print(f"✅ 成功切换到 {config['name']}")

                # 获取当前项目的解析器
                parser = manager.get_current_parser()
                if parser:
                    # 演示信号查询
                    all_signals = parser.get_all_signals()
                    print(f"   📊 项目信息: {len(all_signals)} 个信号")

                    # 显示前3个信号
                    print("   🔍 前3个信号:")
                    for i, signal in enumerate(all_signals[:3]):
                        print(f"      {i+1}. {signal.name} (消息: {signal.message_name})")

        # 演示中文关键词搜索
        print_separator("中文关键词搜索演示")
        manager.set_current_project("F2")
        f2_parser = manager.get_current_parser()

        if f2_parser:
            # 测试中文关键词搜索
            chinese_keywords = ["制动", "ADS", "时间"]

            for keyword in chinese_keywords:
                signals = f2_parser.search_signals_fuzzy(keyword, case_sensitive=False)
                print(f"\n🔎 中文关键词: '{keyword}' (F2项目找到 {len(signals)} 个信号)")

                # 显示前3个结果
                for i, signal in enumerate(signals[:3]):
                    print(f"   {i+1}. {signal.name}")
                    print(f"      └─ 消息: {signal.message_name}")
                    if signal.comment:
                        print(f"      └─ 注释: {signal.comment}")
                    if signal.value_descriptions:
                        print(f"      └─ 值描述: {signal.value_descriptions}")

        # 演示信号值描述功能
        print_separator("信号值描述演示")
        if f2_parser:
            # 查找有值描述的信号
            test_signal = f2_parser.get_signal_by_name("ADS_BrkTarTqEna")
            if test_signal:
                print(f"🔍 信号: {test_signal.name}")
                print(f"   - 消息: {test_signal.message_name}")
                print(f"   - 注释: {test_signal.comment}")
                print(f"   - 值描述:")
                for value, description in test_signal.value_descriptions.items():
                    print(f"     {value}: {description}")

        # 演示跨项目对比
        print_separator("跨项目对比演示")
        keyword = "Time"

        for project_key in ["F2", "F3"]:
            parser = manager.get_parser(project_key)
            if parser:
                signals = parser.search_signals_fuzzy(keyword, case_sensitive=False)
                config = manager.project_configs[project_key]
                print(f"\n📊 {config['name']} - 关键词'{keyword}':")
                print(f"   找到 {len(signals)} 个相关信号")

                # 显示前3个结果
                for i, signal in enumerate(signals[:3]):
                    print(f"   {i+1}. {signal.name} (消息: {signal.message_name})")

        print_separator("演示完成")
        print("✅ 多项目功能演示完成！")
        print("\n💡 新功能特性:")
        print("   - ✨ 多项目DBC文件管理")
        print("   - 🔄 动态项目切换")
        print("   - 🔍 中文关键词搜索")
        print("   - 📝 信号值描述解析")
        print("   - 📊 跨项目对比分析")
        print("\n🚀 启动服务器:")
        print("   python start_server.py")
        print("\n🧪 运行测试:")
        print("   python test_multi_project.py")

    except Exception as e:
        print(f"❌ 演示过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_multi_project_manager()
