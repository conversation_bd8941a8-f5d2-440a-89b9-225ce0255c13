#!/usr/bin/env python3
"""
测试DBC解析器改进功能
测试中文搜索和信号值描述功能
"""

from dbc_parser import DBCParser

def test_signal_value_descriptions():
    """测试信号值描述功能"""
    print("=== 测试信号值描述功能 ===")
    
    parser = DBCParser('F2Project.dbc')
    
    # 测试ADS_BrkTarTqEna信号
    signal = parser.get_signal_by_name('ADS_BrkTarTqEna')
    if signal:
        print(f"信号名称: {signal.name}")
        print(f"原始注释: {repr(signal.comment)}")
        
        # 尝试解码中文注释
        if signal.comment:
            try:
                decoded_comment = signal.comment.encode('latin1').decode('gbk')
                print(f"解码后注释: {decoded_comment}")
            except:
                print(f"注释解码失败: {signal.comment}")
        
        print(f"值描述: {signal.value_descriptions}")
        if signal.value_descriptions:
            for value, desc in signal.value_descriptions.items():
                print(f"  {value}: {desc}")
    else:
        print("未找到ADS_BrkTarTqEna信号")
    
    print()

def test_chinese_search():
    """测试中文搜索功能"""
    print("=== 测试中文搜索功能 ===")
    
    parser = DBCParser('F2Project.dbc')
    
    # 测试搜索"制动"相关信号
    print("搜索关键词: '制动'")
    results = parser.search_signals_fuzzy('制动')
    print(f"找到 {len(results)} 个相关信号")
    
    # 测试搜索"ADS"相关信号
    print("\n搜索关键词: 'ADS'")
    results = parser.search_signals_fuzzy('ADS')
    print(f"找到 {len(results)} 个相关信号")
    for i, signal in enumerate(results[:5]):
        print(f"  {i+1}. {signal.name}")
        if signal.comment:
            try:
                decoded_comment = signal.comment.encode('latin1').decode('gbk')
                print(f"     注释: {decoded_comment}")
            except:
                print(f"     注释: {signal.comment}")
        if signal.value_descriptions:
            print(f"     值描述: {signal.value_descriptions}")
    
    print()

def test_mcp_server():
    """测试MCP服务器功能"""
    print("=== 测试MCP服务器功能 ===")
    
    from can_dbc_mcp_server import initialize_dbc_parser, get_signal_exact, search_signals_fuzzy
    
    # 初始化DBC解析器
    initialize_dbc_parser('F2Project.dbc')
    
    # 测试精确查询
    print("测试精确查询 ADS_BrkTarTqEna:")
    result = get_signal_exact('ADS_BrkTarTqEna')
    print(f"查询结果: {result['found']}")
    if result['found']:
        signal = result['signal']
        print(f"  信号名称: {signal['name']}")
        print(f"  消息名称: {signal['message_name']}")
        print(f"  值描述: {signal.get('value_descriptions_formatted', '无')}")
    
    # 测试模糊查询
    print("\n测试模糊查询 'ADS':")
    result = search_signals_fuzzy('ADS', max_results=3)
    print(f"找到 {result['total_found']} 个信号，返回 {result['returned_count']} 个")
    for signal in result['signals']:
        print(f"  - {signal['name']}: {signal.get('value_descriptions_formatted', '无值描述')}")
    
    print()

if __name__ == "__main__":
    test_signal_value_descriptions()
    test_chinese_search()
    test_mcp_server()
