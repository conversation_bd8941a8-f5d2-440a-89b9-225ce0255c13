"""
测试DBC解析器功能的脚本
"""

from dbc_parser import <PERSON>CPars<PERSON>


def test_dbc_parser():
    """测试DBC解析器的各项功能"""
    print("=== 测试DBC解析器 ===")
    
    try:
        # 初始化解析器
        parser = DBCParser("F2Project.dbc")
        print("✓ DBC文件加载成功")
        
        # 获取统计信息
        stats = parser.get_statistics()
        print(f"✓ 统计信息: {stats}")
        
        # 测试精确查询
        print("\n=== 测试精确查询 ===")
        test_signals = ["TBOX_TimeYear", "EMS_engSpeed", "ABM_IMU_LongAcceleration"]
        
        for signal_name in test_signals:
            signal_info = parser.get_signal_by_name(signal_name)
            if signal_info:
                print(f"✓ 找到信号 '{signal_name}':")
                print(f"  - 消息: {signal_info.message_name} (ID: 0x{signal_info.message_id:X})")
                print(f"  - 位置: 起始位{signal_info.start_bit}, 长度{signal_info.length}位")
                print(f"  - 范围: [{signal_info.minimum}, {signal_info.maximum}] {signal_info.unit}")
                print(f"  - 因子: {signal_info.factor}, 偏移: {signal_info.offset}")
            else:
                print(f"✗ 未找到信号 '{signal_name}'")
        
        # 测试模糊查询
        print("\n=== 测试模糊查询 ===")
        test_keywords = ["Time", "EMS", "ABM"]
        
        for keyword in test_keywords:
            signals = parser.search_signals_fuzzy(keyword)
            print(f"✓ 关键词 '{keyword}' 找到 {len(signals)} 个信号:")
            for i, signal in enumerate(signals[:3]):  # 只显示前3个
                print(f"  {i+1}. {signal.name} (消息: {signal.message_name})")
            if len(signals) > 3:
                print(f"  ... 还有 {len(signals) - 3} 个信号")
        
        # 测试消息查询
        print("\n=== 测试消息查询 ===")
        test_messages = ["TBOX_TimeInfo", "VIU1_EMS_357", "ABM_11F"]
        
        for message_name in test_messages:
            message_info = parser.get_message_by_name(message_name)
            if message_info:
                print(f"✓ 找到消息 '{message_name}':")
                print(f"  - ID: 0x{message_info.message_id:X} ({message_info.message_id})")
                print(f"  - 长度: {message_info.length} 字节")
                print(f"  - 发送者: {message_info.sender}")
                print(f"  - 信号数量: {len(message_info.signals)}")
            else:
                print(f"✗ 未找到消息 '{message_name}'")
        
        # 测试根据消息获取信号
        print("\n=== 测试根据消息获取信号 ===")
        message_name = "TBOX_TimeInfo"
        signals = parser.get_signals_by_message(message_name)
        print(f"✓ 消息 '{message_name}' 包含 {len(signals)} 个信号:")
        for signal in signals:
            print(f"  - {signal.name}: 位{signal.start_bit}-{signal.start_bit + signal.length - 1} ({signal.unit})")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False


def test_mcp_tools():
    """测试MCP工具函数逻辑"""
    print("\n=== 测试MCP工具函数逻辑 ===")

    try:
        # 直接测试工具函数的逻辑，而不是通过MCP框架
        from dbc_parser import DBCParser

        # 初始化解析器
        parser = DBCParser("F2Project.dbc")
        print("✓ DBC解析器初始化成功")

        # 模拟精确查询工具
        print("\n--- 测试精确查询逻辑 ---")
        signal_name = "TBOX_TimeYear"
        signal_info = parser.get_signal_by_name(signal_name)

        if signal_info:
            print(f"✓ 精确查询成功: {signal_name}")
            print(f"  - 消息: {signal_info.message_name} (ID: 0x{signal_info.message_id:X})")
            print(f"  - 位置: 起始位{signal_info.start_bit}, 长度{signal_info.length}位")
            print(f"  - 范围: [{signal_info.minimum}, {signal_info.maximum}] {signal_info.unit}")
        else:
            print(f"✗ 精确查询失败: {signal_name}")

        # 模拟模糊查询工具
        print("\n--- 测试模糊查询逻辑 ---")
        keyword = "Time"
        signals = parser.search_signals_fuzzy(keyword, case_sensitive=False)
        limited_signals = signals[:5]  # 限制结果数量

        print(f"✓ 模糊查询成功: 关键词 '{keyword}' 找到 {len(signals)} 个信号，返回前 {len(limited_signals)} 个")

        # 模拟消息查询工具
        print("\n--- 测试消息查询逻辑 ---")
        message_name = "TBOX_TimeInfo"
        message_info = parser.get_message_by_name(message_name)

        if message_info:
            print(f"✓ 消息查询成功: {message_name}")
            print(f"  - ID: 0x{message_info.message_id:X}")
            print(f"  - 信号数量: {len(message_info.signals)}")
        else:
            print(f"✗ 消息查询失败: {message_name}")

        # 模拟统计信息工具
        print("\n--- 测试统计信息逻辑 ---")
        stats = parser.get_statistics()
        print(f"✓ 统计信息获取成功: {stats}")

        print("\n=== MCP工具逻辑测试完成 ===")
        return True

    except Exception as e:
        print(f"✗ MCP工具逻辑测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 运行测试
    parser_test_ok = test_dbc_parser()
    mcp_test_ok = test_mcp_tools()
    
    if parser_test_ok and mcp_test_ok:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
