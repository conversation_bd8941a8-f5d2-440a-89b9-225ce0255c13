"""
DBC文件解析器模块
使用cantools库解析DBC文件，提供CAN信号查询功能
支持多项目DBC文件管理
"""

import cantools
from typing import Dict, List, Optional, Any
import os
import re
from dataclasses import dataclass


@dataclass
class SignalInfo:
    """CAN信号信息数据类"""
    name: str
    message_name: str
    message_id: int
    start_bit: int
    length: int
    factor: float
    offset: float
    minimum: Optional[float]
    maximum: Optional[float]
    unit: str
    receivers: List[str]
    byte_order: str
    is_signed: bool
    comment: Optional[str] = None
    value_descriptions: Optional[Dict[int, str]] = None


@dataclass
class MessageInfo:
    """CAN消息信息数据类"""
    name: str
    message_id: int
    length: int
    sender: str
    signals: List[str]
    comment: Optional[str] = None


class DBCParser:
    """DBC文件解析器类"""

    # 配置参数：保留的信号值描述数量上限
    DEFAULT_MAX_VALUE_DESCRIPTIONS = 20

    def __init__(self, dbc_file_path: str, max_value_descriptions: int = None):
        """
        初始化DBC解析器

        Args:
            dbc_file_path: DBC文件路径
            max_value_descriptions: 保留的信号值描述数量上限，默认为20
        """
        self.dbc_file_path = dbc_file_path
        self.database = None
        self.signals_dict: Dict[str, SignalInfo] = {}
        self.messages_dict: Dict[str, MessageInfo] = {}
        self.signal_value_descriptions: Dict[str, Dict[int, str]] = {}

        # 设置值描述数量限制（可配置）
        if max_value_descriptions is not None:
            self.max_value_descriptions = max_value_descriptions
        else:
            self.max_value_descriptions = self.DEFAULT_MAX_VALUE_DESCRIPTIONS

        self.load_dbc()
    
    def load_dbc(self) -> None:
        """加载并解析DBC文件"""
        if not os.path.exists(self.dbc_file_path):
            raise FileNotFoundError(f"DBC文件不存在: {self.dbc_file_path}")

        try:
            self.database = cantools.database.load_file(self.dbc_file_path)
            self._parse_value_descriptions()
            self._parse_messages_and_signals()
        except Exception as e:
            raise Exception(f"解析DBC文件失败: {str(e)}")

    def _parse_value_descriptions(self) -> None:
        """解析信号值描述（VAL_定义），保留key值在0到配置上限范围内的值描述"""
        try:
            with open(self.dbc_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # 匹配VAL_定义的正则表达式
            # VAL_ 283 ADS_BrkTarTqEna 1 "Enable" 0 "Disable" ;
            val_pattern = r'VAL_\s+(\d+)\s+(\w+)\s+((?:\d+\s+"[^"]*"\s*)+);'

            for match in re.finditer(val_pattern, content):
                message_id = int(match.group(1))
                signal_name = match.group(2)
                values_str = match.group(3)

                # 解析值和描述对
                value_desc_pattern = r'(\d+)\s+"([^"]*)"'
                all_value_descriptions = {}

                # 首先收集所有值描述
                for value_match in re.finditer(value_desc_pattern, values_str):
                    value = int(value_match.group(1))
                    description = value_match.group(2)
                    all_value_descriptions[value] = description

                # 按key值排序，只保留0到配置上限范围内的值描述
                filtered_value_descriptions = {}
                for value in sorted(all_value_descriptions.keys()):
                    if 0 <= value <= self.max_value_descriptions:
                        filtered_value_descriptions[value] = all_value_descriptions[value]

                if filtered_value_descriptions:
                    self.signal_value_descriptions[signal_name] = filtered_value_descriptions

        except Exception as e:
            print(f"解析VAL_定义时出错: {str(e)}")
  

    def _parse_messages_and_signals(self) -> None:
        """解析消息和信号信息"""
        for message in self.database.messages:
            # 解析消息信息
            signal_names = [signal.name for signal in message.signals]

            # 处理中文注释编码
            message_comment = getattr(message, 'comment', None)
            if message_comment:
                try:
                    # 尝试从latin1解码为GBK
                    message_comment = message_comment.encode('latin1').decode('gbk')
                except:
                    # 如果解码失败，保持原样
                    pass

            message_info = MessageInfo(
                name=message.name,
                message_id=message.frame_id,
                length=message.length,
                sender=getattr(message, 'senders', ['Unknown'])[0] if hasattr(message, 'senders') and message.senders else 'Unknown',
                signals=signal_names,
                comment=message_comment
            )
            self.messages_dict[message.name] = message_info
            
            # 解析信号信息
            for signal in message.signals:
                # 处理中文注释编码
                comment = getattr(signal, 'comment', None)
                if comment:
                    try:
                        # 尝试从latin1解码为GBK
                        comment = comment.encode('latin1').decode('gbk')
                    except:
                        # 如果解码失败，保持原样
                        pass

                signal_info = SignalInfo(
                    name=signal.name,
                    message_name=message.name,
                    message_id=message.frame_id,
                    start_bit=signal.start,
                    length=signal.length,
                    factor=signal.scale,
                    offset=signal.offset,
                    minimum=signal.minimum,
                    maximum=signal.maximum,
                    unit=signal.unit or "",
                    receivers=list(signal.receivers) if signal.receivers else [],
                    byte_order="little_endian" if signal.byte_order == "little_endian" else "big_endian",
                    is_signed=signal.is_signed,
                    comment=comment,
                    value_descriptions=self.signal_value_descriptions.get(signal.name)
                )
                self.signals_dict[signal.name] = signal_info
    
    def get_signal_by_name(self, signal_name: str) -> Optional[SignalInfo]:
        """
        根据信号名称精确查询信号信息
        
        Args:
            signal_name: 信号名称
            
        Returns:
            SignalInfo对象或None
        """
        return self.signals_dict.get(signal_name)
    
    def _split_keywords(self, keyword: str) -> List[str]:
        """
        分割关键词字符串为单个关键词列表
        支持空格、逗号等分隔符
        """
        import re
        # 使用正则表达式分割，支持空格、逗号、分号等分隔符
        keywords = re.split(r'[,，\s;；]+', keyword.strip())
        # 过滤空字符串并去除首尾空格
        return [kw.strip() for kw in keywords if kw.strip()]

    def _calculate_match_score(self, signal_info: SignalInfo, keywords: List[str], case_sensitive: bool = False) -> float:
        """
        计算信号与关键词的匹配分数

        Args:
            signal_info: 信号信息
            keywords: 关键词列表
            case_sensitive: 是否区分大小写

        Returns:
            匹配分数 (0-1之间，1表示完全匹配)
        """
        if not keywords:
            return 0.0

        total_score = 0.0
        matched_keywords = 0

        # 准备搜索文本
        search_texts = []

        # 信号名称 (权重最高)
        signal_name = signal_info.name if case_sensitive else signal_info.name.lower()
        search_texts.append(("name", signal_name, 1.0))

        # 消息名称 (中等权重)
        message_name = signal_info.message_name if case_sensitive else signal_info.message_name.lower()
        search_texts.append(("message", message_name, 0.8))

        # 信号注释 (中等权重)
        if signal_info.comment:
            comment = signal_info.comment if case_sensitive else signal_info.comment.lower()
            search_texts.append(("comment", comment, 0.7))

        # 值描述 (较低权重)
        if signal_info.value_descriptions:
            for value, description in signal_info.value_descriptions.items():
                desc = description if case_sensitive else description.lower()
                search_texts.append(("value_desc", desc, 0.5))

        # 单位 (较低权重)
        if signal_info.unit:
            unit = signal_info.unit if case_sensitive else signal_info.unit.lower()
            search_texts.append(("unit", unit, 0.4))

        # 对每个关键词计算匹配分数
        for keyword in keywords:
            if not case_sensitive:
                keyword = keyword.lower()

            keyword_score = 0.0
            keyword_matched = False

            for text_type, text, weight in search_texts:
                if keyword in text:
                    keyword_matched = True
                    # 计算匹配质量
                    if text == keyword:  # 完全匹配
                        match_quality = 1.0
                    elif text.startswith(keyword) or text.endswith(keyword):  # 前缀或后缀匹配
                        match_quality = 0.8
                    else:  # 包含匹配
                        match_quality = 0.6

                    # 根据文本类型调整权重
                    if text_type == "name":
                        match_quality *= 1.2  # 信号名称匹配加权

                    keyword_score = max(keyword_score, weight * match_quality)

            if keyword_matched:
                matched_keywords += 1
                total_score += keyword_score

        # 计算最终分数：匹配关键词比例 * 平均匹配质量
        if matched_keywords == 0:
            return 0.0

        keyword_coverage = matched_keywords / len(keywords)
        average_quality = total_score / matched_keywords

        return keyword_coverage * average_quality

    def search_signals_fuzzy(self, keyword: str, case_sensitive: bool = False) -> List[SignalInfo]:
        """
        模糊查询信号信息（支持多关键词、信号名称、注释、值描述搜索）

        Args:
            keyword: 搜索关键词（支持多个关键词，用空格、逗号等分隔）
            case_sensitive: 是否区分大小写

        Returns:
            按匹配分数排序的SignalInfo对象列表
        """
        # 分割关键词
        keywords = self._split_keywords(keyword)
        if not keywords:
            return []

        # 计算每个信号的匹配分数
        signal_scores = []
        for signal_name, signal_info in self.signals_dict.items():
            score = self._calculate_match_score(signal_info, keywords, case_sensitive)
            if score > 0:
                signal_scores.append((signal_info, score))

        # 按分数降序排序
        signal_scores.sort(key=lambda x: x[1], reverse=True)

        # 返回排序后的信号列表
        return [signal_info for signal_info, score in signal_scores]
    
    def get_message_by_name(self, message_name: str) -> Optional[MessageInfo]:
        """
        根据消息名称查询消息信息
        
        Args:
            message_name: 消息名称
            
        Returns:
            MessageInfo对象或None
        """
        return self.messages_dict.get(message_name)
    
    def _calculate_message_match_score(self, message_info: MessageInfo, keywords: List[str], case_sensitive: bool = False) -> float:
        """
        计算消息与关键词的匹配分数

        Args:
            message_info: 消息信息
            keywords: 关键词列表
            case_sensitive: 是否区分大小写

        Returns:
            匹配分数 (0-1之间)
        """
        if not keywords:
            return 0.0

        total_score = 0.0
        matched_keywords = 0

        # 准备搜索文本
        search_texts = []

        # 消息名称 (权重最高)
        message_name = message_info.name if case_sensitive else message_info.name.lower()
        search_texts.append(("name", message_name, 1.0))

        # 发送者 (中等权重)
        sender = message_info.sender if case_sensitive else message_info.sender.lower()
        search_texts.append(("sender", sender, 0.8))

        # 消息注释 (中等权重)
        if message_info.comment:
            comment = message_info.comment if case_sensitive else message_info.comment.lower()
            search_texts.append(("comment", comment, 0.7))

        # 对每个关键词计算匹配分数
        for keyword in keywords:
            if not case_sensitive:
                keyword = keyword.lower()

            keyword_score = 0.0
            keyword_matched = False

            for text_type, text, weight in search_texts:
                if keyword in text:
                    keyword_matched = True
                    # 计算匹配质量
                    if text == keyword:  # 完全匹配
                        match_quality = 1.0
                    elif text.startswith(keyword) or text.endswith(keyword):  # 前缀或后缀匹配
                        match_quality = 0.8
                    else:  # 包含匹配
                        match_quality = 0.6

                    keyword_score = max(keyword_score, weight * match_quality)

            if keyword_matched:
                matched_keywords += 1
                total_score += keyword_score

        # 计算最终分数
        if matched_keywords == 0:
            return 0.0

        keyword_coverage = matched_keywords / len(keywords)
        average_quality = total_score / matched_keywords

        return keyword_coverage * average_quality

    def search_messages_fuzzy(self, keyword: str, case_sensitive: bool = False) -> List[MessageInfo]:
        """
        模糊查询消息信息（支持多关键词搜索）

        Args:
            keyword: 搜索关键词（支持多个关键词，用空格、逗号等分隔）
            case_sensitive: 是否区分大小写

        Returns:
            按匹配分数排序的MessageInfo对象列表
        """
        # 分割关键词
        keywords = self._split_keywords(keyword)
        if not keywords:
            return []

        # 计算每个消息的匹配分数
        message_scores = []
        for message_name, message_info in self.messages_dict.items():
            score = self._calculate_message_match_score(message_info, keywords, case_sensitive)
            if score > 0:
                message_scores.append((message_info, score))

        # 按分数降序排序
        message_scores.sort(key=lambda x: x[1], reverse=True)

        # 返回排序后的消息列表
        return [message_info for message_info, score in message_scores]
    
    def get_all_signals(self) -> List[SignalInfo]:
        """获取所有信号信息"""
        return list(self.signals_dict.values())
    
    def get_all_messages(self) -> List[MessageInfo]:
        """获取所有消息信息"""
        return list(self.messages_dict.values())
    
    def get_signals_by_message(self, message_name: str) -> List[SignalInfo]:
        """
        根据消息名称获取该消息下的所有信号
        
        Args:
            message_name: 消息名称
            
        Returns:
            该消息下的所有SignalInfo对象列表
        """
        return [signal for signal in self.signals_dict.values() 
                if signal.message_name == message_name]
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取DBC文件统计信息
        
        Returns:
            包含统计信息的字典
        """
        return {
            "total_messages": len(self.messages_dict),
            "total_signals": len(self.signals_dict),
            "dbc_file": self.dbc_file_path,
            "database_name": getattr(self.database, 'name', 'Unknown') if self.database else 'Unknown'
        }


class MultiProjectDBCManager:
    """多项目DBC文件管理器"""

    def __init__(self, max_value_descriptions: int = None):
        self.projects: Dict[str, DBCParser] = {}
        self.current_project: Optional[str] = None

        # 设置值描述数量限制（可配置）
        self.max_value_descriptions = max_value_descriptions or DBCParser.DEFAULT_MAX_VALUE_DESCRIPTIONS

        # 预定义的项目配置
        self.project_configs = {
            "F2": {
                "name": "F2项目",
                "file": "F2Project.dbc",
                "description": "F2项目CAN信号数据库"
            },
            "F3": {
                "name": "F3项目",
                "file": "F3Project.dbc",
                "description": "F3项目CAN信号数据库"
            }
        }

    def load_project(self, project_key: str) -> bool:
        """加载指定项目的DBC文件"""
        if project_key not in self.project_configs:
            return False

        config = self.project_configs[project_key]
        dbc_file = config["file"]

        if not os.path.exists(dbc_file):
            print(f"DBC文件不存在: {dbc_file}")
            return False

        try:
            # 传递值描述数量限制配置
            self.projects[project_key] = DBCParser(dbc_file, max_value_descriptions=self.max_value_descriptions)
            print(f"成功加载项目 {config['name']}: {dbc_file}")
            return True
        except Exception as e:
            print(f"加载项目 {config['name']} 失败: {str(e)}")
            return False

    def load_all_projects(self) -> Dict[str, bool]:
        """加载所有可用的项目"""
        results = {}
        for project_key in self.project_configs:
            results[project_key] = self.load_project(project_key)
        return results

    def set_current_project(self, project_key: str) -> bool:
        """设置当前活动项目"""
        if project_key not in self.projects:
            if not self.load_project(project_key):
                return False

        self.current_project = project_key
        return True

    def get_current_parser(self) -> Optional[DBCParser]:
        """获取当前项目的解析器"""
        if self.current_project and self.current_project in self.projects:
            return self.projects[self.current_project]
        return None

    def get_parser(self, project_key: str) -> Optional[DBCParser]:
        """获取指定项目的解析器"""
        if project_key in self.projects:
            return self.projects[project_key]
        elif self.load_project(project_key):
            return self.projects[project_key]
        return None

    def get_available_projects(self) -> List[Dict[str, str]]:
        """获取可用项目列表"""
        projects = []
        for key, config in self.project_configs.items():
            status = "已加载" if key in self.projects else "未加载"
            current = " (当前)" if key == self.current_project else ""
            projects.append({
                "key": key,
                "name": config["name"],
                "file": config["file"],
                "description": config["description"],
                "status": status + current,
                "loaded": key in self.projects,
                "current": key == self.current_project
            })
        return projects

    def get_project_statistics(self) -> Dict[str, Any]:
        """获取所有项目的统计信息"""
        stats = {
            "total_projects": len(self.project_configs),
            "loaded_projects": len(self.projects),
            "current_project": self.current_project,
            "projects": {}
        }

        for key, parser in self.projects.items():
            config = self.project_configs[key]
            project_stats = parser.get_statistics()
            project_stats["project_name"] = config["name"]
            project_stats["project_description"] = config["description"]
            stats["projects"][key] = project_stats

        return stats
