#!/usr/bin/env python3
"""
测试MCP客户端
用于测试CAN DBC MCP服务器的功能
"""

import json
import subprocess
import sys
import time

def test_mcp_server():
    """测试MCP服务器功能"""
    print("=== 测试MCP服务器功能 ===")
    
    # 测试精确查询
    print("1. 测试精确查询 ADS_BrkTarTqEna:")
    test_request = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "get_signal_exact",
            "arguments": {
                "signal_name": "ADS_BrkTarTqEna"
            }
        }
    }
    
    print(f"   请求: {json.dumps(test_request, indent=2)}")
    
    # 测试模糊查询
    print("\n2. 测试模糊查询 '制动':")
    test_request2 = {
        "jsonrpc": "2.0", 
        "id": 2,
        "method": "tools/call",
        "params": {
            "name": "search_signals_fuzzy",
            "arguments": {
                "keyword": "制动",
                "max_results": 3
            }
        }
    }
    
    print(f"   请求: {json.dumps(test_request2, indent=2)}")
    
    print("\n注意：要实际测试MCP服务器，需要使用MCP客户端连接到服务器")
    print("可以使用Cline或其他MCP客户端来测试")

def test_direct_functions():
    """直接测试函数功能"""
    print("\n=== 直接测试函数功能 ===")
    
    # 导入并初始化
    from can_dbc_mcp_server import initialize_dbc_parser, format_signal_info
    from dbc_parser import DBCParser
    
    # 初始化解析器
    initialize_dbc_parser('F2Project.dbc')
    
    # 创建解析器实例来测试
    parser = DBCParser('F2Project.dbc')
    
    # 测试精确查询
    print("1. 测试精确查询:")
    signal = parser.get_signal_by_name('ADS_BrkTarTqEna')
    if signal:
        formatted = format_signal_info(signal)
        print(f"   信号名称: {formatted['name']}")
        print(f"   消息名称: {formatted['message_name']}")
        print(f"   注释: {formatted['comment']}")
        print(f"   值描述: {formatted['value_descriptions_formatted']}")
    
    # 测试模糊查询
    print("\n2. 测试模糊查询:")
    results = parser.search_signals_fuzzy('制动')
    print(f"   找到 {len(results)} 个信号:")
    for signal in results[:3]:  # 只显示前3个
        formatted = format_signal_info(signal)
        print(f"   - {formatted['name']}: {formatted['comment']}")
        if formatted['value_descriptions']:
            print(f"     值描述: {formatted['value_descriptions_formatted']}")

if __name__ == "__main__":
    test_mcp_server()
    test_direct_functions()
