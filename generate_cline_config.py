#!/usr/bin/env python3
"""
自动生成Cline MCP配置的脚本
"""

import os
import json
import sys
import platform

def get_current_directory():
    """获取当前目录的绝对路径"""
    return os.path.abspath(os.getcwd())

def get_python_command():
    """获取Python命令"""
    system = platform.system().lower()
    
    # 尝试不同的Python命令
    python_commands = ['python', 'python3', 'py']
    
    for cmd in python_commands:
        try:
            import subprocess
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return cmd
        except:
            continue
    
    # 默认返回
    return 'python3' if system in ['linux', 'darwin'] else 'python'

def normalize_path(path):
    """标准化路径格式"""
    # 转换为绝对路径
    abs_path = os.path.abspath(path)
    
    # Windows系统使用双反斜杠
    if platform.system().lower() == 'windows':
        return abs_path.replace('\\', '\\\\')
    else:
        return abs_path

def generate_config():
    """生成Cline MCP配置"""
    print("=== Cline MCP配置生成器 ===")
    
    # 获取当前目录
    current_dir = get_current_directory()
    print(f"当前工作目录: {current_dir}")
    
    # 检查必要文件
    required_files = ['can_dbc_mcp_server.py', 'F2Project.dbc', 'dbc_parser.py']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(os.path.join(current_dir, file)):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        print("请确保在正确的项目目录中运行此脚本")
        return False
    
    print("✅ 所有必要文件都存在")
    
    # 获取Python命令
    python_cmd = get_python_command()
    print(f"检测到Python命令: {python_cmd}")
    
    # 标准化路径
    normalized_path = normalize_path(current_dir)
    print(f"标准化路径: {normalized_path}")
    
    # 生成配置
    config = {
        "cline.mcpServers": {
            "can-dbc-parser": {
                "name": "CAN DBC Parser",
                "description": "CAN DBC文件解析服务器，提供CAN信号的精确查询和模糊查询功能",
                "command": python_cmd,
                "args": ["can_dbc_mcp_server.py"],
                "cwd": normalized_path,
                "env": {
                    "PYTHONPATH": normalized_path,
                    "PYTHONUNBUFFERED": "1"
                },
                "timeout": 30000
            }
        }
    }
    
    # 保存配置文件
    config_file = "cline_mcp_config.json"
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 配置文件已生成: {config_file}")
        
        # 显示配置内容
        print("\n📋 生成的配置内容:")
        print("=" * 50)
        print(json.dumps(config, indent=2, ensure_ascii=False))
        print("=" * 50)
        
        # 提供使用说明
        print("\n🔧 使用说明:")
        print("1. 复制上面的配置内容")
        print("2. 打开VS Code设置 (Ctrl+,)")
        print("3. 搜索 'mcp' 找到Cline MCP设置")
        print("4. 点击 'Edit in settings.json'")
        print("5. 将配置内容添加到settings.json文件中")
        print("6. 保存文件并重启VS Code")
        
        print(f"\n💡 或者直接使用生成的配置文件: {config_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成配置文件失败: {str(e)}")
        return False

def test_server():
    """测试MCP服务器是否可以启动"""
    print("\n🧪 测试MCP服务器...")
    
    try:
        import subprocess
        python_cmd = get_python_command()
        
        # 尝试导入必要模块
        result = subprocess.run([
            python_cmd, '-c', 
            'import fastmcp, cantools, dbc_parser; print("所有依赖模块导入成功")'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 依赖检查通过")
            print("✅ MCP服务器应该可以正常启动")
            return True
        else:
            print("❌ 依赖检查失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("CAN DBC MCP Server - Cline配置生成器")
    print("=" * 50)
    
    # 生成配置
    config_success = generate_config()
    
    if config_success:
        # 测试服务器
        test_success = test_server()
        
        if test_success:
            print("\n🎉 配置生成完成！")
            print("\n📖 详细配置说明请参考: CLINE_MCP_SETUP.md")
        else:
            print("\n⚠️  配置已生成，但服务器测试失败")
            print("请检查Python环境和依赖包安装")
    else:
        print("\n❌ 配置生成失败")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
