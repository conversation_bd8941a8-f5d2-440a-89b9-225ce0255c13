# Cline MCP 配置指南

本指南将帮助您在Cline中配置CAN DBC MCP Server，以便在Cline中使用CAN信号查询功能。

## 📋 前提条件

1. **已安装Cline扩展** (VS Code)
2. **Python环境** 已配置并安装了必要依赖
3. **CAN DBC MCP Server** 已构建完成

## 🔧 配置步骤

### 方法1: 通过VS Code设置界面配置

1. **打开VS Code设置**
   - 按 `Ctrl+,` (Windows/Linux) 或 `Cmd+,` (Mac)
   - 或者通过菜单: File → Preferences → Settings

2. **搜索MCP设置**
   - 在设置搜索框中输入 "mcp"
   - 找到 "Cline: MCP Servers" 设置项

3. **添加MCP服务器配置**
   - 点击 "Edit in settings.json"
   - 添加以下配置:

```json
{
  "cline.mcpServers": {
    "can-dbc-parser": {
      "name": "CAN DBC Parser",
      "description": "CAN DBC文件解析服务器，提供CAN信号查询功能",
      "command": "python",
      "args": ["can_dbc_mcp_server.py"],
      "cwd": "e:\\SAI_MCP",
      "env": {
        "PYTHONPATH": "e:\\SAI_MCP",
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

### 方法2: 直接编辑settings.json文件

1. **打开VS Code用户设置文件**
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 输入 "Preferences: Open User Settings (JSON)"
   - 选择该选项

2. **添加MCP配置**
   - 在settings.json文件中添加或更新以下配置:

```json
{
  "cline.mcpServers": {
    "can-dbc-parser": {
      "name": "CAN DBC Parser", 
      "description": "CAN DBC文件解析服务器，提供CAN信号的精确查询和模糊查询功能",
      "command": "python",
      "args": ["can_dbc_mcp_server.py"],
      "cwd": "e:\\SAI_MCP",
      "env": {
        "PYTHONPATH": "e:\\SAI_MCP",
        "PYTHONUNBUFFERED": "1"
      },
      "timeout": 30000
    }
  }
}
```

### 方法3: 使用配置文件

如果Cline支持外部配置文件，您可以使用我们提供的配置文件:

1. **复制配置文件**
   ```bash
   cp cline_mcp_settings.json ~/.cline/mcp_settings.json
   ```

2. **或者在Cline设置中指定配置文件路径**

## 🔧 配置参数说明

| 参数 | 说明 | 值 |
|------|------|-----|
| `name` | 服务器显示名称 | "CAN DBC Parser" |
| `description` | 服务器描述 | 功能说明 |
| `command` | 启动命令 | "python" |
| `args` | 命令参数 | ["can_dbc_mcp_server.py"] |
| `cwd` | 工作目录 | "e:\\SAI_MCP" |
| `env.PYTHONPATH` | Python路径 | "e:\\SAI_MCP" |
| `env.PYTHONUNBUFFERED` | 禁用Python缓冲 | "1" |
| `timeout` | 超时时间(毫秒) | 30000 |

## ⚠️ 重要注意事项

1. **路径配置**
   - 请将 `cwd` 和 `PYTHONPATH` 中的路径 `e:\\SAI_MCP` 替换为您的实际项目路径
   - Windows路径使用双反斜杠 `\\` 或正斜杠 `/`
   - Linux/Mac路径使用正斜杠 `/`

2. **Python环境**
   - 确保 `python` 命令可以在命令行中正常执行
   - 如果使用虚拟环境，请指定完整的Python路径

3. **依赖检查**
   - 确保已安装所有必要的Python包 (fastmcp, cantools等)

## 🚀 启动和测试

### 1. 重启Cline
配置完成后，重启VS Code或重新加载Cline扩展。

### 2. 验证连接
在Cline聊天界面中，您应该能看到CAN DBC Parser服务器已连接。

### 3. 测试工具
尝试使用以下命令测试MCP工具:

```
请帮我查询信号 TBOX_TimeYear 的详细信息
```

```
请搜索包含关键词 "Time" 的所有CAN信号
```

```
请获取消息 TBOX_TimeInfo 的信息
```

## 🛠️ 故障排除

### 问题1: 服务器无法启动
**解决方案:**
- 检查Python路径是否正确
- 确认工作目录路径正确
- 验证依赖包是否已安装

### 问题2: 找不到DBC文件
**解决方案:**
- 确认F2Project.dbc文件在正确位置
- 检查文件权限

### 问题3: 连接超时
**解决方案:**
- 增加timeout值
- 检查防火墙设置
- 确认Python环境正常

## 📝 配置模板

### Windows配置模板
```json
{
  "cline.mcpServers": {
    "can-dbc-parser": {
      "name": "CAN DBC Parser",
      "command": "python",
      "args": ["can_dbc_mcp_server.py"],
      "cwd": "C:\\path\\to\\your\\SAI_MCP",
      "env": {
        "PYTHONPATH": "C:\\path\\to\\your\\SAI_MCP"
      }
    }
  }
}
```

### Linux/Mac配置模板
```json
{
  "cline.mcpServers": {
    "can-dbc-parser": {
      "name": "CAN DBC Parser",
      "command": "python3",
      "args": ["can_dbc_mcp_server.py"],
      "cwd": "/path/to/your/SAI_MCP",
      "env": {
        "PYTHONPATH": "/path/to/your/SAI_MCP"
      }
    }
  }
}
```

## ✅ 验证配置成功

配置成功后，您应该能够:

1. **看到服务器状态**: Cline界面显示CAN DBC Parser已连接
2. **使用MCP工具**: 可以调用6个CAN查询工具
3. **获得查询结果**: 能够成功查询CAN信号和消息信息

## 🎯 下一步

配置完成后，您就可以在Cline中使用以下功能:

- 🔍 精确查询CAN信号
- 🔎 模糊搜索CAN信号  
- 📨 查询CAN消息信息
- 📊 获取DBC统计信息
- 📋 按消息获取信号列表

享受强大的CAN信号查询功能吧！
