#!/usr/bin/env python3
"""
简化测试脚本
"""

from dbc_parser import DBCParser

def main():
    print("=== 测试改进功能 ===")
    
    parser = DBCParser('F2Project.dbc')
    
    # 测试信号值描述
    print("1. 测试信号值描述:")
    signal = parser.get_signal_by_name('ADS_BrkTarTqEna')
    if signal:
        print(f"   信号: {signal.name}")
        print(f"   注释: {signal.comment}")
        print(f"   值描述: {signal.value_descriptions}")
    
    # 测试中文搜索
    print("\n2. 测试中文搜索:")
    results = parser.search_signals_fuzzy('制动')
    print(f"   搜索'制动'找到 {len(results)} 个信号")
    for signal in results[:3]:
        print(f"   - {signal.name}: {signal.comment}")
    
    # 测试英文搜索
    print("\n3. 测试英文搜索:")
    results = parser.search_signals_fuzzy('Brk')
    print(f"   搜索'Brk'找到 {len(results)} 个信号")
    for signal in results[:3]:
        print(f"   - {signal.name}: {signal.comment}")
    
    # 测试值描述搜索
    print("\n4. 测试值描述搜索:")
    results = parser.search_signals_fuzzy('Enable')
    print(f"   搜索'Enable'找到 {len(results)} 个信号")
    for signal in results[:3]:
        print(f"   - {signal.name}: {signal.value_descriptions}")

if __name__ == "__main__":
    main()
