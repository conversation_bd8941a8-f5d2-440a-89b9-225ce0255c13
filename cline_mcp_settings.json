{"mcpServers": {"can-dbc-parser": {"name": "CAN DBC Parser", "description": "CAN DBC文件解析服务器，提供CAN信号的精确查询和模糊查询功能", "command": "python", "args": ["can_dbc_mcp_server.py"], "cwd": "e:\\SAI_MCP", "env": {"PYTHONPATH": "e:\\SAI_MCP", "PYTHONUNBUFFERED": "1"}, "timeout": 30000, "capabilities": {"tools": true, "resources": false, "prompts": false}}}, "globalSettings": {"timeout": 30000, "retries": 3, "logLevel": "info"}}