#!/usr/bin/env python3
"""
多项目DBC解析器测试脚本
测试F2和F3项目的功能
"""

from dbc_parser import MultiProjectDBCManager

def test_multi_project_functionality():
    """测试多项目功能"""
    print("=== 多项目DBC解析器测试 ===\n")
    
    # 初始化项目管理器
    manager = MultiProjectDBCManager()
    
    # 加载所有项目
    print("1. 加载项目:")
    results = manager.load_all_projects()
    for project_key, success in results.items():
        config = manager.project_configs[project_key]
        status = "成功" if success else "失败"
        print(f"   {config['name']}: {status}")
    
    # 获取项目列表
    print("\n2. 可用项目列表:")
    projects = manager.get_available_projects()
    for project in projects:
        print(f"   {project['key']}: {project['name']} - {project['status']}")
    
    # 设置当前项目为F2
    print("\n3. 切换到F2项目:")
    success = manager.set_current_project("F2")
    print(f"   切换结果: {'成功' if success else '失败'}")
    
    # 测试F2项目信号查询
    print("\n4. F2项目信号查询测试:")
    f2_parser = manager.get_current_parser()
    if f2_parser:
        # 测试精确查询
        signal = f2_parser.get_signal_by_name("ADS_BrkTarTqEna")
        if signal:
            print(f"   找到信号: {signal.name}")
            print(f"   消息: {signal.message_name}")
            print(f"   值描述: {signal.value_descriptions}")
        
        # 测试模糊查询
        signals = f2_parser.search_signals_fuzzy("ADS制动", case_sensitive=False)
        print(f"   模糊查询'ADS制动'找到 {len(signals)} 个信号")
        for sig in signals[:3]:  # 显示前3个
            print(f"     - {sig.name}")
    
    # 切换到F3项目
    print("\n5. 切换到F3项目:")
    success = manager.set_current_project("F3")
    print(f"   切换结果: {'成功' if success else '失败'}")
    
    # 测试F3项目信号查询
    print("\n6. F3项目信号查询测试:")
    f3_parser = manager.get_current_parser()
    if f3_parser:
        # 获取所有信号的前5个
        all_signals = f3_parser.get_all_signals()
        print(f"   F3项目总信号数: {len(all_signals)}")
        print("   前5个信号:")
        for sig in all_signals[:5]:
            print(f"     - {sig.name} (消息: {sig.message_name})")
    
    # 获取项目统计信息
    print("\n7. 项目统计信息:")
    stats = manager.get_project_statistics()
    print(f"   总项目数: {stats['total_projects']}")
    print(f"   已加载项目数: {stats['loaded_projects']}")
    print(f"   当前项目: {stats['current_project']}")
    
    for project_key, project_stats in stats['projects'].items():
        print(f"   {project_key}项目:")
        print(f"     - 消息数: {project_stats['total_messages']}")
        print(f"     - 信号数: {project_stats['total_signals']}")
        print(f"     - 文件: {project_stats['dbc_file']}")

if __name__ == "__main__":
    test_multi_project_functionality()
