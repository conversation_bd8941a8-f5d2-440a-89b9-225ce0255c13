"""
CAN DBC MCP Server
基于FastMCP框架的CAN DBC文件解析服务器
提供CAN信号的精确查询和模糊查询功能
"""

import os
from typing import List, Optional, Dict, Any
from fastmcp import FastMCP
from dbc_parser import DBCParser, SignalInfo, MessageInfo, MultiProjectDBCManager

# 初始化MCP服务器
mcp = FastMCP("CAN DBC Multi-Project Parser")

# 全局多项目管理器实例
project_manager: Optional[MultiProjectDBCManager] = None

def initialize_project_manager(max_value_descriptions: int = 20) -> None:
    """
    初始化多项目管理器

    Args:
        max_value_descriptions: 保留的信号值描述数量上限，默认为20
    """
    global project_manager
    try:
        project_manager = MultiProjectDBCManager(max_value_descriptions=max_value_descriptions)
        # 加载所有可用项目
        results = project_manager.load_all_projects()
        print("项目加载结果:")
        for project_key, success in results.items():
            config = project_manager.project_configs[project_key]
            status = "成功" if success else "失败"
            print(f"  {config['name']}: {status}")

        # 设置默认项目为F2
        if "F2" in results and results["F2"]:
            project_manager.set_current_project("F2")
            print("默认项目设置为: F2项目")
        elif "F3" in results and results["F3"]:
            project_manager.set_current_project("F3")
            print("默认项目设置为: F3项目")

        stats = project_manager.get_project_statistics()
        print(f"项目统计信息: {stats}")
    except Exception as e:
        print(f"初始化项目管理器失败: {str(e)}")
        raise


def format_signal_info(signal_info) -> Dict[str, Any]:
    """格式化信号信息为字典"""
    signal_dict = {
        "name": signal_info.name,
        "message_name": signal_info.message_name,
        "message_id": f"0x{signal_info.message_id:X}",
        "message_id_decimal": signal_info.message_id,
        "start_bit": signal_info.start_bit,
        "length": signal_info.length,
        "factor": signal_info.factor,
        "offset": signal_info.offset,
        "minimum": signal_info.minimum,
        "maximum": signal_info.maximum,
        "unit": signal_info.unit,
        "receivers": signal_info.receivers,
        "byte_order": signal_info.byte_order,
        "is_signed": signal_info.is_signed,
        "comment": signal_info.comment
    }

    # 添加信号值描述
    if signal_info.value_descriptions:
        signal_dict["value_descriptions"] = signal_info.value_descriptions
        # 格式化值描述为易读的字符串
        desc_list = [f"{value}: {desc}" for value, desc in signal_info.value_descriptions.items()]
        signal_dict["value_descriptions_formatted"] = ", ".join(desc_list)
    else:
        signal_dict["value_descriptions"] = None
        signal_dict["value_descriptions_formatted"] = "无值描述"

    return signal_dict


def create_compact_signal_info(signal_info, include_details: bool = True) -> Dict[str, Any]:
    """
    创建紧凑的信号信息格式（考虑值描述限制）

    Args:
        signal_info: 信号信息对象
        include_details: 是否包含详细信息
    """
    if include_details:
        return format_signal_info(signal_info)
    else:
        # 紧凑格式，只包含关键信息
        compact_info = {
            "name": signal_info.name,
            "message_name": signal_info.message_name,
            "message_id": f"0x{signal_info.message_id:X}",
            "start_bit": signal_info.start_bit,
            "length": signal_info.length,
            "unit": signal_info.unit,
            "comment": signal_info.comment[:50] + "..." if signal_info.comment and len(signal_info.comment) > 50 else signal_info.comment
        }

        # 只在有值描述时添加（已在解析时限制为20个）
        if signal_info.value_descriptions:
            # 紧凑模式只显示前5个值描述
            limited_desc = dict(list(signal_info.value_descriptions.items())[:5])
            compact_info["value_descriptions"] = limited_desc
            if len(signal_info.value_descriptions) > 5:
                compact_info["value_descriptions_count"] = len(signal_info.value_descriptions)
                compact_info["value_descriptions_note"] = f"显示前5个，共{len(signal_info.value_descriptions)}个"

        return compact_info


@mcp.tool()
def get_available_projects() -> Dict[str, Any]:
    """
    获取可用项目列表

    Returns:
        可用项目信息和当前项目状态
    """
    if not project_manager:
        return {"error": "项目管理器未初始化"}

    projects = project_manager.get_available_projects()
    stats = project_manager.get_project_statistics()

    return {
        "available_projects": projects,
        "current_project": stats["current_project"],
        "total_projects": stats["total_projects"],
        "loaded_projects": stats["loaded_projects"]
    }


@mcp.tool()
def switch_project(project_key: str) -> Dict[str, Any]:
    """
    切换当前活动项目

    Args:
        project_key: 项目键值 (F2, F3)

    Returns:
        切换结果和项目信息
    """
    if not project_manager:
        return {"error": "项目管理器未初始化"}

    if project_key not in project_manager.project_configs:
        available_keys = list(project_manager.project_configs.keys())
        return {
            "error": f"无效的项目键值: {project_key}",
            "available_projects": available_keys
        }

    success = project_manager.set_current_project(project_key)
    if success:
        config = project_manager.project_configs[project_key]
        parser = project_manager.get_current_parser()
        stats = parser.get_statistics() if parser else {}

        return {
            "success": True,
            "current_project": project_key,
            "project_name": config["name"],
            "project_description": config["description"],
            "statistics": stats
        }
    else:
        return {
            "error": f"切换到项目 {project_key} 失败",
            "success": False
        }


@mcp.tool()
def get_signal_exact(signal_name: str, project_key: Optional[str] = None) -> Dict[str, Any]:
    """
    精确查询CAN信号信息

    Args:
        signal_name: 信号名称（精确匹配）
        project_key: 项目键值 (F2, F3)，如果不指定则使用当前项目

    Returns:
        信号详细信息字典，如果未找到则返回错误信息
    """
    if not project_manager:
        return {"error": "项目管理器未初始化"}

    # 获取解析器
    if project_key:
        parser = project_manager.get_parser(project_key)
        if not parser:
            return {"error": f"项目 {project_key} 不存在或加载失败"}
    else:
        parser = project_manager.get_current_parser()
        if not parser:
            return {"error": "当前没有活动项目，请先切换项目"}
        project_key = project_manager.current_project

    signal_info = parser.get_signal_by_name(signal_name)

    if signal_info:
        result = format_signal_info(signal_info)
        result["project_key"] = project_key
        result["project_name"] = project_manager.project_configs[project_key]["name"]

        return {
            "found": True,
            "signal": result
        }
    else:
        return {
            "found": False,
            "error": f"在项目 {project_key} 中未找到信号: {signal_name}",
            "project_key": project_key
        }


@mcp.tool()
def search_signals_fuzzy(keyword: str, case_sensitive: bool = False, max_results: int = 50, project_key: Optional[str] = None) -> Dict[str, Any]:
    """
    模糊查询CAN信号信息（支持中文关键词搜索）

    Args:
        keyword: 搜索关键词（支持部分匹配，可搜索信号名称、注释、值描述）
        case_sensitive: 是否区分大小写，默认False
        max_results: 最大返回结果数量，默认50
        project_key: 项目键值 (F2, F3)，如果不指定则使用当前项目

    Returns:
        匹配的信号列表和统计信息，包含信号值描述
    """
    if not project_manager:
        return {"error": "项目管理器未初始化"}

    if not keyword.strip():
        return {"error": "搜索关键词不能为空"}

    # 获取解析器
    if project_key:
        parser = project_manager.get_parser(project_key)
        if not parser:
            return {"error": f"项目 {project_key} 不存在或加载失败"}
    else:
        parser = project_manager.get_current_parser()
        if not parser:
            return {"error": "当前没有活动项目，请先切换项目"}
        project_key = project_manager.current_project

    signals = parser.search_signals_fuzzy(keyword, case_sensitive)

    # 限制返回结果数量
    limited_signals = signals[:max_results]

    result_signals = []
    for signal_info in limited_signals:
        signal_dict = format_signal_info(signal_info)
        signal_dict["project_key"] = project_key
        result_signals.append(signal_dict)

    return {
        "keyword": keyword,
        "case_sensitive": case_sensitive,
        "total_found": len(signals),
        "returned_count": len(result_signals),
        "max_results": max_results,
        "project_key": project_key,
        "project_name": project_manager.project_configs[project_key]["name"],
        "signals": result_signals
    }


@mcp.tool()
def get_message_info(message_name: str, project_key: Optional[str] = None) -> Dict[str, Any]:
    """
    查询CAN消息信息

    Args:
        message_name: 消息名称
        project_key: 项目键值 (F2, F3)，如果不指定则使用当前项目

    Returns:
        消息详细信息字典
    """
    if not project_manager:
        return {"error": "项目管理器未初始化"}

    # 获取解析器
    if project_key:
        parser = project_manager.get_parser(project_key)
        if not parser:
            return {"error": f"项目 {project_key} 不存在或加载失败"}
    else:
        parser = project_manager.get_current_parser()
        if not parser:
            return {"error": "当前没有活动项目，请先切换项目"}
        project_key = project_manager.current_project

    message_info = parser.get_message_by_name(message_name)

    if message_info:
        return {
            "found": True,
            "message": {
                "name": message_info.name,
                "message_id": f"0x{message_info.message_id:X}",
                "message_id_decimal": message_info.message_id,
                "length": message_info.length,
                "sender": message_info.sender,
                "signals": message_info.signals,
                "signal_count": len(message_info.signals),
                "comment": message_info.comment,
                "project_key": project_key,
                "project_name": project_manager.project_configs[project_key]["name"]
            }
        }
    else:
        return {
            "found": False,
            "error": f"在项目 {project_key} 中未找到消息: {message_name}",
            "project_key": project_key
        }


@mcp.tool()
def search_messages_fuzzy(keyword: str, case_sensitive: bool = False, max_results: int = 50, project_key: Optional[str] = None) -> Dict[str, Any]:
    """
    模糊查询CAN消息信息

    Args:
        keyword: 搜索关键词
        case_sensitive: 是否区分大小写，默认False
        max_results: 最大返回结果数量，默认50
        project_key: 项目键值 (F2, F3)，如果不指定则使用当前项目

    Returns:
        匹配的消息列表和统计信息
    """
    if not project_manager:
        return {"error": "项目管理器未初始化"}

    if not keyword.strip():
        return {"error": "搜索关键词不能为空"}

    # 获取解析器
    if project_key:
        parser = project_manager.get_parser(project_key)
        if not parser:
            return {"error": f"项目 {project_key} 不存在或加载失败"}
    else:
        parser = project_manager.get_current_parser()
        if not parser:
            return {"error": "当前没有活动项目，请先切换项目"}
        project_key = project_manager.current_project

    messages = parser.search_messages_fuzzy(keyword, case_sensitive)
    
    # 限制返回结果数量
    limited_messages = messages[:max_results]
    
    result_messages = []
    for message_info in limited_messages:
        result_messages.append({
            "name": message_info.name,
            "message_id": f"0x{message_info.message_id:X}",
            "message_id_decimal": message_info.message_id,
            "length": message_info.length,
            "sender": message_info.sender,
            "signals": message_info.signals,
            "signal_count": len(message_info.signals),
            "comment": message_info.comment,
            "project_key": project_key
        })

    return {
        "keyword": keyword,
        "case_sensitive": case_sensitive,
        "total_found": len(messages),
        "returned_count": len(result_messages),
        "max_results": max_results,
        "project_key": project_key,
        "project_name": project_manager.project_configs[project_key]["name"],
        "messages": result_messages
    }


@mcp.tool()
def get_signals_by_message(message_name: str, project_key: Optional[str] = None) -> Dict[str, Any]:
    """
    根据消息名称获取该消息下的所有信号

    Args:
        message_name: 消息名称
        project_key: 项目键值 (F2, F3)，如果不指定则使用当前项目

    Returns:
        该消息下的所有信号信息
    """
    if not project_manager:
        return {"error": "项目管理器未初始化"}

    # 获取解析器
    if project_key:
        parser = project_manager.get_parser(project_key)
        if not parser:
            return {"error": f"项目 {project_key} 不存在或加载失败"}
    else:
        parser = project_manager.get_current_parser()
        if not parser:
            return {"error": "当前没有活动项目，请先切换项目"}
        project_key = project_manager.current_project

    signals = parser.get_signals_by_message(message_name)

    if not signals:
        return {
            "found": False,
            "error": f"在项目 {project_key} 中消息 '{message_name}' 不存在或没有信号",
            "project_key": project_key
        }

    result_signals = []
    for signal_info in signals:
        signal_dict = format_signal_info(signal_info)
        signal_dict["project_key"] = project_key
        result_signals.append(signal_dict)

    return {
        "message_name": message_name,
        "signal_count": len(result_signals),
        "project_key": project_key,
        "project_name": project_manager.project_configs[project_key]["name"],
        "signals": result_signals
    }


@mcp.tool()
def get_project_statistics(project_key: Optional[str] = None) -> Dict[str, Any]:
    """
    获取项目统计信息

    Args:
        project_key: 项目键值 (F2, F3)，如果不指定则返回所有项目统计信息

    Returns:
        项目统计信息
    """
    if not project_manager:
        return {"error": "项目管理器未初始化"}

    if project_key:
        # 获取指定项目的统计信息
        parser = project_manager.get_parser(project_key)
        if not parser:
            return {"error": f"项目 {project_key} 不存在或加载失败"}

        stats = parser.get_statistics()
        config = project_manager.project_configs[project_key]
        stats["project_key"] = project_key
        stats["project_name"] = config["name"]
        stats["project_description"] = config["description"]
        return stats
    else:
        # 获取所有项目的统计信息
        return project_manager.get_project_statistics()


if __name__ == "__main__":
    # 初始化多项目管理器
    initialize_project_manager()

    # 运行MCP服务器
    mcp.run()
